import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def display_key_results():
    """展示关键分析结果"""
    
    print("=" * 60)
    print("沪深300ETF分时数据线分析 - 关键结果")
    print("=" * 60)
    
    print("\n【问题1】三种线构建结果：")
    print("✓ 分时线 (Tick Bars): 186个")
    print("✓ 成交量线 (Volume Bars): 8,434个") 
    print("✓ 美元线 (Dollar Bars): 8,350个")
    
    print("\n【问题2】每周线数稳定性分析：")
    print("分时线每周数量标准差: 7.17")
    print("成交量线每周数量标准差: 258.71")
    print("美元线每周数量标准差: 260.51")
    print("🏆 最稳定: 分时线 (标准差最小)")
    print("\n原因: 分时线基于固定tick数构建，不受市场波动影响")
    
    print("\n【问题3】序列相关性分析：")
    print("分时线收益率序列相关性: -0.0722")
    print("成交量线收益率序列相关性: 0.0113")
    print("美元线收益率序列相关性: 0.0062")
    print("🏆 序列相关性最低: 美元线 (0.0062)")
    print("\n意义: 美元线收益率更接近随机游走，符合有效市场假设")
    
    print("\n【问题4】月度方差分析：")
    print("分时线平均月度方差: 0.00001556")
    print("成交量线平均月度方差: 0.00000034")
    print("美元线平均月度方差: 0.00000034")
    print("🏆 方差最小: 成交量线和美元线 (并列)")
    print("\n意义: 信息驱动的采样方式产生更稳定的收益率")
    
    print("\n【问题5】Jarque-Bera正态性检验：")
    print("分时线 JB统计量: 4.87 (p值: 0.0875)")
    print("成交量线 JB统计量: 145,256 (p值: 0.0000)")
    print("美元线 JB统计量: 179,559 (p值: 0.0000)")
    print("🏆 最接近正态分布: 分时线 (JB统计量最低)")
    print("\n意义: 分时线收益率分布最接近正态，便于统计分析")
    
    print("\n" + "=" * 60)
    print("总结与建议")
    print("=" * 60)
    
    recommendations = {
        "高频交易": "美元线 - 序列相关性最低",
        "风险管理": "成交量线/美元线 - 方差最小", 
        "传统分析": "分时线 - 最接近正态分布",
        "市场监控": "分时线 - 产生信号最稳定"
    }
    
    for scenario, recommendation in recommendations.items():
        print(f"📊 {scenario}: {recommendation}")
    
    print(f"\n📁 详细分析报告和图表已保存在 '任务一' 文件夹中")
    print("📈 包含文件:")
    print("   - etf_analysis.py (完整分析代码)")
    print("   - 分析报告.md (详细报告)")
    print("   - weekly_bar_counts.png (每周线数图)")
    print("   - returns_series.png (收益率序列图)")
    print("   - monthly_variance.png (月度方差图)")
    print("   - jarque_bera_analysis.png (正态性检验图)")

def create_summary_table():
    """创建结果汇总表"""
    
    # 创建汇总数据
    data = {
        '指标': ['线数量', '每周稳定性(标准差)', '序列相关性(绝对值)', '月度方差', 'JB统计量'],
        '分时线': [186, 7.17, 0.0722, 0.00001556, 4.87],
        '成交量线': [8434, 258.71, 0.0113, 0.00000034, 145256],
        '美元线': [8350, 260.51, 0.0062, 0.00000034, 179559],
        '最优选择': ['成交量线', '分时线', '美元线', '成交量线/美元线', '分时线']
    }
    
    df = pd.DataFrame(data)
    
    print("\n" + "=" * 80)
    print("结果汇总表")
    print("=" * 80)
    print(df.to_string(index=False))
    
    # 保存汇总表
    df.to_csv('任务一/结果汇总表.csv', index=False, encoding='utf-8-sig')
    print(f"\n📊 汇总表已保存为: 任务一/结果汇总表.csv")

if __name__ == "__main__":
    display_key_results()
    create_summary_table()
    
    print("\n" + "=" * 60)
    print("分析完成！")
    print("=" * 60)
