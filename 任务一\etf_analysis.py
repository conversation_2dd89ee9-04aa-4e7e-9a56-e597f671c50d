import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ETFLineAnalysis:
    def __init__(self, data_path):
        """初始化ETF线分析类"""
        self.data_path = data_path
        self.df = None
        self.tick_bars = None
        self.volume_bars = None
        self.dollar_bars = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.df = pd.read_csv(self.data_path)
        
        # 转换时间格式
        self.df['datetime'] = pd.to_datetime(self.df['date'] + ' ' + self.df['tick'])
        self.df = self.df.sort_values('datetime').reset_index(drop=True)
        
        # 计算美元成交量
        self.df['dollar_volume'] = self.df['price'] * self.df['vol']
        
        print(f"数据加载完成，共{len(self.df)}条记录")
        print(f"时间范围：{self.df['datetime'].min()} 到 {self.df['datetime'].max()}")
        
    def create_tick_bars(self, tick_threshold=1000):
        """构建分时线（Tick Bars）"""
        print(f"构建分时线，阈值：{tick_threshold}")
        
        bars = []
        current_ticks = 0
        start_idx = 0
        
        for i, row in self.df.iterrows():
            current_ticks += 1
            
            if current_ticks >= tick_threshold or i == len(self.df) - 1:
                # 创建一个bar
                subset = self.df.iloc[start_idx:i+1]
                
                bar = {
                    'datetime': subset['datetime'].iloc[-1],
                    'open': subset['price'].iloc[0],
                    'high': subset['price'].max(),
                    'low': subset['price'].min(),
                    'close': subset['price'].iloc[-1],
                    'volume': subset['vol'].sum(),
                    'dollar_volume': subset['dollar_volume'].sum(),
                    'tick_count': len(subset)
                }
                bars.append(bar)
                
                # 重置计数器
                current_ticks = 0
                start_idx = i + 1
        
        self.tick_bars = pd.DataFrame(bars)
        print(f"分时线构建完成，共{len(self.tick_bars)}个bars")
        
    def create_volume_bars(self, volume_threshold=50000):
        """构建成交量线（Volume Bars）"""
        print(f"构建成交量线，阈值：{volume_threshold}")
        
        bars = []
        current_volume = 0
        start_idx = 0
        
        for i, row in self.df.iterrows():
            current_volume += row['vol']
            
            if current_volume >= volume_threshold or i == len(self.df) - 1:
                # 创建一个bar
                subset = self.df.iloc[start_idx:i+1]
                
                bar = {
                    'datetime': subset['datetime'].iloc[-1],
                    'open': subset['price'].iloc[0],
                    'high': subset['price'].max(),
                    'low': subset['price'].min(),
                    'close': subset['price'].iloc[-1],
                    'volume': subset['vol'].sum(),
                    'dollar_volume': subset['dollar_volume'].sum(),
                    'tick_count': len(subset)
                }
                bars.append(bar)
                
                # 重置计数器
                current_volume = 0
                start_idx = i + 1
        
        self.volume_bars = pd.DataFrame(bars)
        print(f"成交量线构建完成，共{len(self.volume_bars)}个bars")
        
    def create_dollar_bars(self, dollar_threshold=200000):
        """构建美元线（Dollar Bars）"""
        print(f"构建美元线，阈值：{dollar_threshold}")
        
        bars = []
        current_dollar = 0
        start_idx = 0
        
        for i, row in self.df.iterrows():
            current_dollar += row['dollar_volume']
            
            if current_dollar >= dollar_threshold or i == len(self.df) - 1:
                # 创建一个bar
                subset = self.df.iloc[start_idx:i+1]
                
                bar = {
                    'datetime': subset['datetime'].iloc[-1],
                    'open': subset['price'].iloc[0],
                    'high': subset['price'].max(),
                    'low': subset['price'].min(),
                    'close': subset['price'].iloc[-1],
                    'volume': subset['vol'].sum(),
                    'dollar_volume': subset['dollar_volume'].sum(),
                    'tick_count': len(subset)
                }
                bars.append(bar)
                
                # 重置计数器
                current_dollar = 0
                start_idx = i + 1
        
        self.dollar_bars = pd.DataFrame(bars)
        print(f"美元线构建完成，共{len(self.dollar_bars)}个bars")
        
    def calculate_returns(self, bars_df):
        """计算收益率"""
        returns = bars_df['close'].pct_change().dropna()
        return returns
        
    def weekly_bar_count(self):
        """计算每周线数并绘制时间序列"""
        print("\n=== 问题2：每周线数统计 ===")
        
        # 为每个bars添加周信息
        for bars, name in [(self.tick_bars, 'Tick'), (self.volume_bars, 'Volume'), (self.dollar_bars, 'Dollar')]:
            bars['week'] = bars['datetime'].dt.isocalendar().week
            bars['year'] = bars['datetime'].dt.year
            bars['year_week'] = bars['year'].astype(str) + '-W' + bars['week'].astype(str).str.zfill(2)
        
        # 统计每周线数
        tick_weekly = self.tick_bars.groupby('year_week').size()
        volume_weekly = self.volume_bars.groupby('year_week').size()
        dollar_weekly = self.dollar_bars.groupby('year_week').size()
        
        # 创建完整的周列表
        all_weeks = sorted(set(tick_weekly.index) | set(volume_weekly.index) | set(dollar_weekly.index))
        
        # 重新索引以确保所有周都存在
        tick_weekly = tick_weekly.reindex(all_weeks, fill_value=0)
        volume_weekly = volume_weekly.reindex(all_weeks, fill_value=0)
        dollar_weekly = dollar_weekly.reindex(all_weeks, fill_value=0)
        
        # 计算稳定性（标准差）
        tick_std = tick_weekly.std()
        volume_std = volume_weekly.std()
        dollar_std = dollar_weekly.std()
        
        print(f"分时线每周数量标准差: {tick_std:.2f}")
        print(f"成交量线每周数量标准差: {volume_std:.2f}")
        print(f"美元线每周数量标准差: {dollar_std:.2f}")
        
        # 找出最稳定的线类型
        stds = {'Tick': tick_std, 'Volume': volume_std, 'Dollar': dollar_std}
        most_stable = min(stds, key=stds.get)
        print(f"\n最稳定的线类型: {most_stable}线 (标准差: {stds[most_stable]:.2f})")
        
        # 绘制时间序列图
        plt.figure(figsize=(15, 8))
        plt.plot(range(len(tick_weekly)), tick_weekly.values, label='分时线', marker='o')
        plt.plot(range(len(volume_weekly)), volume_weekly.values, label='成交量线', marker='s')
        plt.plot(range(len(dollar_weekly)), dollar_weekly.values, label='美元线', marker='^')
        
        plt.xlabel('周数')
        plt.ylabel('线数量')
        plt.title('每周线数时间序列')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(range(0, len(all_weeks), 2), [all_weeks[i] for i in range(0, len(all_weeks), 2)], rotation=45)
        plt.tight_layout()
        plt.savefig('任务一/weekly_bar_counts.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return tick_weekly, volume_weekly, dollar_weekly

    def serial_correlation_analysis(self):
        """计算3种线的回报序列相关性"""
        print("\n=== 问题3：序列相关性分析 ===")

        # 计算各种线的收益率
        tick_returns = self.calculate_returns(self.tick_bars)
        volume_returns = self.calculate_returns(self.volume_bars)
        dollar_returns = self.calculate_returns(self.dollar_bars)

        # 计算序列相关性（滞后1期的自相关）
        tick_corr = tick_returns.autocorr(lag=1)
        volume_corr = volume_returns.autocorr(lag=1)
        dollar_corr = dollar_returns.autocorr(lag=1)

        print(f"分时线收益率序列相关性: {tick_corr:.4f}")
        print(f"成交量线收益率序列相关性: {volume_corr:.4f}")
        print(f"美元线收益率序列相关性: {dollar_corr:.4f}")

        # 找出序列相关性最低的
        corrs = {'Tick': abs(tick_corr), 'Volume': abs(volume_corr), 'Dollar': abs(dollar_corr)}
        lowest_corr = min(corrs, key=corrs.get)
        print(f"\n序列相关性最低的线类型: {lowest_corr}线 (相关性: {corrs[lowest_corr]:.4f})")

        # 绘制收益率时间序列
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        axes[0].plot(tick_returns.values)
        axes[0].set_title(f'分时线收益率 (序列相关性: {tick_corr:.4f})')
        axes[0].grid(True, alpha=0.3)

        axes[1].plot(volume_returns.values)
        axes[1].set_title(f'成交量线收益率 (序列相关性: {volume_corr:.4f})')
        axes[1].grid(True, alpha=0.3)

        axes[2].plot(dollar_returns.values)
        axes[2].set_title(f'美元线收益率 (序列相关性: {dollar_corr:.4f})')
        axes[2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('任务一/returns_series.png', dpi=300, bbox_inches='tight')
        plt.show()

        return tick_returns, volume_returns, dollar_returns

    def monthly_variance_analysis(self):
        """将线序列拆分为每月数据集，计算每种线的每个子集的回报方差"""
        print("\n=== 问题4：每月方差分析 ===")

        # 为每个bars添加月份信息
        for bars in [self.tick_bars, self.volume_bars, self.dollar_bars]:
            bars['month'] = bars['datetime'].dt.to_period('M')

        # 计算每月收益率方差
        def calculate_monthly_variance(bars_df, name):
            monthly_variances = []
            months = []

            for month in bars_df['month'].unique():
                month_data = bars_df[bars_df['month'] == month]
                if len(month_data) > 1:  # 确保有足够数据计算收益率
                    returns = month_data['close'].pct_change().dropna()
                    if len(returns) > 0:
                        variance = returns.var()
                        monthly_variances.append(variance)
                        months.append(month)

            return pd.Series(monthly_variances, index=months, name=name)

        tick_monthly_var = calculate_monthly_variance(self.tick_bars, 'Tick')
        volume_monthly_var = calculate_monthly_variance(self.volume_bars, 'Volume')
        dollar_monthly_var = calculate_monthly_variance(self.dollar_bars, 'Dollar')

        # 计算平均方差
        tick_avg_var = tick_monthly_var.mean()
        volume_avg_var = volume_monthly_var.mean()
        dollar_avg_var = dollar_monthly_var.mean()

        print(f"分时线平均月度方差: {tick_avg_var:.8f}")
        print(f"成交量线平均月度方差: {volume_avg_var:.8f}")
        print(f"美元线平均月度方差: {dollar_avg_var:.8f}")

        # 找出方差最小的
        variances = {'Tick': tick_avg_var, 'Volume': volume_avg_var, 'Dollar': dollar_avg_var}
        lowest_var = min(variances, key=variances.get)
        print(f"\n方差最小的线类型: {lowest_var}线 (平均方差: {variances[lowest_var]:.8f})")

        # 绘制月度方差图
        plt.figure(figsize=(15, 8))
        x_pos = range(len(tick_monthly_var))
        width = 0.25

        plt.bar([x - width for x in x_pos], tick_monthly_var.values, width, label='分时线', alpha=0.8)
        plt.bar(x_pos, volume_monthly_var.values, width, label='成交量线', alpha=0.8)
        plt.bar([x + width for x in x_pos], dollar_monthly_var.values, width, label='美元线', alpha=0.8)

        plt.xlabel('月份')
        plt.ylabel('收益率方差')
        plt.title('各月份收益率方差比较')
        plt.legend()
        plt.xticks(x_pos, [str(month) for month in tick_monthly_var.index], rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('任务一/monthly_variance.png', dpi=300, bbox_inches='tight')
        plt.show()

        return tick_monthly_var, volume_monthly_var, dollar_monthly_var

    def jarque_bera_test(self):
        """应用Jarque-Bera正态性检验到3种线的回报"""
        print("\n=== 问题5：Jarque-Bera正态性检验 ===")

        # 计算各种线的收益率
        tick_returns = self.calculate_returns(self.tick_bars)
        volume_returns = self.calculate_returns(self.volume_bars)
        dollar_returns = self.calculate_returns(self.dollar_bars)

        # 进行Jarque-Bera检验
        tick_jb_stat, tick_jb_pvalue = stats.jarque_bera(tick_returns.dropna())
        volume_jb_stat, volume_jb_pvalue = stats.jarque_bera(volume_returns.dropna())
        dollar_jb_stat, dollar_jb_pvalue = stats.jarque_bera(dollar_returns.dropna())

        print(f"分时线 Jarque-Bera 统计量: {tick_jb_stat:.4f}, p值: {tick_jb_pvalue:.4f}")
        print(f"成交量线 Jarque-Bera 统计量: {volume_jb_stat:.4f}, p值: {volume_jb_pvalue:.4f}")
        print(f"美元线 Jarque-Bera 统计量: {dollar_jb_stat:.4f}, p值: {dollar_jb_pvalue:.4f}")

        # 找出检验统计量最低的
        jb_stats = {'Tick': tick_jb_stat, 'Volume': volume_jb_stat, 'Dollar': dollar_jb_stat}
        lowest_jb = min(jb_stats, key=jb_stats.get)
        print(f"\nJarque-Bera统计量最低的线类型: {lowest_jb}线 (统计量: {jb_stats[lowest_jb]:.4f})")
        print("注：JB统计量越低，越接近正态分布")

        # 绘制收益率分布图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 直方图
        axes[0, 0].hist(tick_returns.dropna(), bins=50, alpha=0.7, density=True)
        axes[0, 0].set_title(f'分时线收益率分布\nJB统计量: {tick_jb_stat:.4f}')

        axes[0, 1].hist(volume_returns.dropna(), bins=50, alpha=0.7, density=True)
        axes[0, 1].set_title(f'成交量线收益率分布\nJB统计量: {volume_jb_stat:.4f}')

        axes[0, 2].hist(dollar_returns.dropna(), bins=50, alpha=0.7, density=True)
        axes[0, 2].set_title(f'美元线收益率分布\nJB统计量: {dollar_jb_stat:.4f}')

        # Q-Q图
        stats.probplot(tick_returns.dropna(), dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('分时线 Q-Q图')

        stats.probplot(volume_returns.dropna(), dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title('成交量线 Q-Q图')

        stats.probplot(dollar_returns.dropna(), dist="norm", plot=axes[1, 2])
        axes[1, 2].set_title('美元线 Q-Q图')

        plt.tight_layout()
        plt.savefig('任务一/jarque_bera_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return {
            'tick': (tick_jb_stat, tick_jb_pvalue),
            'volume': (volume_jb_stat, volume_jb_pvalue),
            'dollar': (dollar_jb_stat, dollar_jb_pvalue)
        }

    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始ETF线分析...")

        # 加载数据
        self.load_data()

        # 构建三种线
        self.create_tick_bars()
        self.create_volume_bars()
        self.create_dollar_bars()

        # 问题1：构建分时线、成交量线和美元线（已完成）
        print("\n=== 问题1：三种线构建完成 ===")
        print(f"分时线数量: {len(self.tick_bars)}")
        print(f"成交量线数量: {len(self.volume_bars)}")
        print(f"美元线数量: {len(self.dollar_bars)}")

        # 问题2：每周线数统计
        weekly_counts = self.weekly_bar_count()

        # 问题3：序列相关性分析
        returns_data = self.serial_correlation_analysis()

        # 问题4：月度方差分析
        monthly_variances = self.monthly_variance_analysis()

        # 问题5：Jarque-Bera正态性检验
        jb_results = self.jarque_bera_test()

        print("\n=== 分析完成 ===")
        return {
            'weekly_counts': weekly_counts,
            'returns': returns_data,
            'monthly_variances': monthly_variances,
            'jarque_bera': jb_results
        }

def main():
    """主函数"""
    # 数据文件路径
    data_path = "任务一/沪深300ETF_2025.csv"

    # 创建分析对象
    analyzer = ETFLineAnalysis(data_path)

    # 运行完整分析
    results = analyzer.run_complete_analysis()

    print("\n所有分析已完成，结果图表已保存到任务一文件夹中。")

if __name__ == "__main__":
    main()
